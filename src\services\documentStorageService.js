/**
 * Database-Only Document Storage Service
 * Replaces localStorage approach with direct database operations
 * Following Designrr's pattern of server-side content storage
 */

import { projectsService } from './projectsService';

// Configuration for database-only storage
const STORAGE_CONFIG = {
  // Auto-save configuration
  AUTO_SAVE_DEBOUNCE: 2000, // 2 seconds debounce for auto-save
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second base delay
  
  // Cache configuration
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes cache TTL
  MAX_CACHE_SIZE: 10, // Maximum cached documents
};

class DocumentStorageService {
  constructor() {
    this.cache = new Map();
    this.saveTimeouts = new Map();
    this.pendingSaves = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the service
   */
  initialize() {
    if (this.isInitialized) return;
    
    console.log('📚 Initializing Database-Only Document Storage');
    this.isInitialized = true;
  }

  /**
   * Load document from database with caching
   */
  async loadDocument(documentId) {
    try {
      // Check cache first
      const cached = this.getCachedDocument(documentId);
      if (cached) {
        console.log(`📖 Loading document ${documentId} from cache`);
        return { success: true, data: cached, source: 'cache' };
      }

      console.log(`📡 Loading document ${documentId} from database`);
      
      // Fetch from database
      const result = await projectsService.getProject(documentId, true); // Include content
      
      if (result.success) {
        // Cache the document
        this.cacheDocument(documentId, result.data);
        
        console.log(`✅ Document ${documentId} loaded from database`);
        return { success: true, data: result.data, source: 'database' };
      } else {
        console.error(`❌ Failed to load document ${documentId}:`, result.error);
        return { success: false, error: result.error };
      }
      
    } catch (error) {
      console.error(`❌ Error loading document ${documentId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Save document content to database with debounced auto-save
   */
  async saveDocument(documentId, documentData, options = {}) {
    const { immediate = false, skipCache = false } = options;
    
    try {
      // Update cache immediately for optimistic UI
      if (!skipCache) {
        this.cacheDocument(documentId, documentData);
      }

      if (immediate) {
        // Immediate save (for critical operations)
        return await this.performDatabaseSave(documentId, documentData);
      } else {
        // Debounced auto-save
        return this.scheduleAutoSave(documentId, documentData);
      }
      
    } catch (error) {
      console.error(`❌ Error saving document ${documentId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Schedule debounced auto-save
   */
  scheduleAutoSave(documentId, documentData) {
    // Clear existing timeout
    if (this.saveTimeouts.has(documentId)) {
      clearTimeout(this.saveTimeouts.get(documentId));
    }

    // Store pending data
    this.pendingSaves.set(documentId, documentData);

    // Schedule new save
    const timeout = setTimeout(async () => {
      const pendingData = this.pendingSaves.get(documentId);
      if (pendingData) {
        await this.performDatabaseSave(documentId, pendingData);
        this.pendingSaves.delete(documentId);
      }
      this.saveTimeouts.delete(documentId);
    }, STORAGE_CONFIG.AUTO_SAVE_DEBOUNCE);

    this.saveTimeouts.set(documentId, timeout);
    
    console.log(`⏰ Auto-save scheduled for document ${documentId}`);
    return { success: true, scheduled: true };
  }

  /**
   * Perform actual database save with retry logic
   */
  async performDatabaseSave(documentId, documentData, attempt = 1) {
    try {
      console.log(`💾 Saving document ${documentId} to database (attempt ${attempt})`);
      
      // Prepare data for database
      const updateData = {
        generated_content: documentData.generatedContent || null,
        questionnaire_data: documentData,
        word_count: documentData.generatedContent?.wordCount || 0,
        chapter_count: documentData.generatedContent?.chapters?.length || 0,
        updated_at: new Date().toISOString()
      };

      // Save to database
      const result = await projectsService.updateProject(documentId, updateData);
      
      if (result.success) {
        // Update cache with fresh data
        this.cacheDocument(documentId, {
          ...documentData,
          ...result.data,
          lastSaved: new Date().toISOString()
        });
        
        console.log(`✅ Document ${documentId} saved to database`);
        return { success: true, data: result.data };
      } else {
        throw new Error(result.error?.message || 'Database save failed');
      }
      
    } catch (error) {
      console.error(`❌ Database save failed for document ${documentId}:`, error);
      
      // Retry logic
      if (attempt < STORAGE_CONFIG.RETRY_ATTEMPTS) {
        const delay = STORAGE_CONFIG.RETRY_DELAY * Math.pow(2, attempt - 1);
        console.log(`🔄 Retrying save in ${delay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.performDatabaseSave(documentId, documentData, attempt + 1);
      }
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Force immediate save of all pending documents
   */
  async flushPendingSaves() {
    const pendingIds = Array.from(this.pendingSaves.keys());
    
    if (pendingIds.length === 0) {
      return { success: true, saved: 0 };
    }

    console.log(`🚀 Flushing ${pendingIds.length} pending saves`);
    
    const results = await Promise.allSettled(
      pendingIds.map(async (documentId) => {
        const data = this.pendingSaves.get(documentId);
        const result = await this.performDatabaseSave(documentId, data);
        
        if (result.success) {
          this.pendingSaves.delete(documentId);
          if (this.saveTimeouts.has(documentId)) {
            clearTimeout(this.saveTimeouts.get(documentId));
            this.saveTimeouts.delete(documentId);
          }
        }
        
        return result;
      })
    );

    const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    console.log(`✅ Flush completed: ${successCount}/${pendingIds.length} successful`);
    
    return { success: true, saved: successCount, total: pendingIds.length };
  }

  /**
   * Cache management methods
   */
  cacheDocument(documentId, documentData) {
    // Implement LRU cache
    if (this.cache.size >= STORAGE_CONFIG.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(documentId, {
      data: documentData,
      timestamp: Date.now(),
      ttl: Date.now() + STORAGE_CONFIG.CACHE_TTL
    });
  }

  getCachedDocument(documentId) {
    const cached = this.cache.get(documentId);
    
    if (!cached) return null;
    
    // Check TTL
    if (Date.now() > cached.ttl) {
      this.cache.delete(documentId);
      return null;
    }
    
    // Move to end (LRU)
    this.cache.delete(documentId);
    this.cache.set(documentId, cached);
    
    return cached.data;
  }

  invalidateCache(documentId) {
    this.cache.delete(documentId);
    console.log(`🗑️ Cache invalidated for document ${documentId}`);
  }

  clearCache() {
    this.cache.clear();
    console.log('🧹 Document cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: STORAGE_CONFIG.MAX_CACHE_SIZE,
      documents: Array.from(this.cache.keys()),
      pendingSaves: Array.from(this.pendingSaves.keys()).length
    };
  }

  /**
   * Cleanup method for component unmount
   */
  cleanup() {
    // Clear all timeouts
    for (const timeout of this.saveTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.saveTimeouts.clear();
    
    // Flush any pending saves
    this.flushPendingSaves();
    
    console.log('🧹 Document storage service cleaned up');
  }
}

// Create singleton instance
export const documentStorage = new DocumentStorageService();

// Auto-initialize
if (typeof window !== 'undefined') {
  documentStorage.initialize();
  
  // Flush pending saves before page unload
  window.addEventListener('beforeunload', () => {
    documentStorage.flushPendingSaves();
  });
}

export default documentStorage;
